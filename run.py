#!/usr/bin/env python3
"""
OTA服务器启动脚本
"""

import os
import sys
from app import app, init_db

def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 初始化数据库
    print("正在初始化数据库...")
    init_db()
    print("数据库初始化完成")
    
    # 获取配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print(f"启动OTA服务器...")
    print(f"地址: http://{host}:{port}")
    print(f"调试模式: {'开启' if debug else '关闭'}")
    print(f"环境: {os.environ.get('FLASK_ENV', 'development')}")
    
    # 启动应用
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
