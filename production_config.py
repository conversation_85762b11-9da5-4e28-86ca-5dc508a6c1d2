"""
生产环境配置文件
简化配置，专注稳定性和文件管理
"""

import os
from datetime import timedelta

class ProductionConfig:
    """生产环境配置"""
    
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your-production-secret-key-change-this')
    DEBUG = False
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///instance/ota_config.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0
    }
    
    # 文件上传配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', '/home/<USER>/ota_server/static/ota')
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB
    ALLOWED_EXTENSIONS = {'bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf', 'apk'}
    
    # OTA服务配置
    DOWNLOAD_BASE_URL = os.environ.get('DOWNLOAD_BASE_URL', 'http://ota.tyw.com/ota/')
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = '/home/<USER>/ota_server/logs/ota_server.log'
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # 性能配置
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=12)
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        import logging
        from logging.handlers import RotatingFileHandler
        import os
        
        # 确保必要目录存在
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)
        
        # 配置日志
        if not app.debug and not app.testing:
            file_handler = RotatingFileHandler(
                app.config['LOG_FILE'],
                maxBytes=app.config['LOG_MAX_BYTES'],
                backupCount=app.config['LOG_BACKUP_COUNT']
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('OTA Server startup in production mode')

# 开发环境配置（简化版）
class DevelopmentConfig:
    """开发环境配置"""
    
    SECRET_KEY = 'dev-secret-key'
    DEBUG = True
    TESTING = False
    
    SQLALCHEMY_DATABASE_URI = 'sqlite:///ota_config.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    UPLOAD_FOLDER = 'static/ota'
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024
    ALLOWED_EXTENSIONS = {'bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf', 'apk'}
    
    DOWNLOAD_BASE_URL = 'http://localhost:8000/ota/'
    
    WTF_CSRF_ENABLED = True
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    @staticmethod
    def init_app(app):
        import os
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
