version: '3.8'

services:
  ota-server:
    build: .
    container_name: ota-server
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=sqlite:///instance/ota_config.db
      - DOWNLOAD_BASE_URL=${DOWNLOAD_BASE_URL:-http://localhost:8000/static/ota/}
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-123456}
      - MAX_CONTENT_LENGTH=524288000  # 500MB
    volumes:
      - ./instance:/app/instance
      - ./static/ota:/app/static/ota
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选: 添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ota-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./static/ota:/var/www/ota:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ota-server
    restart: unless-stopped
    profiles:
      - with-nginx

volumes:
  ota_data:
    driver: local
