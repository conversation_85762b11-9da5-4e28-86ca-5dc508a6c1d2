from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, PasswordField, TextAreaField, BooleanField, SelectField, IntegerField
from wtforms.validators import DataRequired, Length, Email, Optional, ValidationError, Regexp
from models import User, Device, DeviceGroup

class LoginForm(FlaskForm):
    """登录表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='用户名不能为空'),
        Length(min=3, max=64, message='用户名长度必须在3-64个字符之间')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='密码不能为空'),
        Length(min=6, message='密码长度至少6个字符')
    ])
    remember_me = BooleanField('记住我')

class UserForm(FlaskForm):
    """用户表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='用户名不能为空'),
        Length(min=3, max=64, message='用户名长度必须在3-64个字符之间'),
        Regexp(r'^[a-zA-Z0-9_]+$', message='用户名只能包含字母、数字和下划线')
    ])
    email = StringField('邮箱', validators=[
        Optional(),
        Email(message='邮箱格式不正确'),
        Length(max=120, message='邮箱长度不能超过120个字符')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='密码不能为空'),
        Length(min=6, max=128, message='密码长度必须在6-128个字符之间')
    ])
    is_admin = BooleanField('管理员权限')
    is_active = BooleanField('激活状态', default=True)
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        if user:
            # 编辑模式，密码可选
            self.password.validators = [Optional(), Length(min=6, max=128)]
    
    def validate_username(self, field):
        """验证用户名唯一性"""
        user = User.query.filter_by(username=field.data).first()
        if user and (not self.user or user.id != self.user.id):
            raise ValidationError('用户名已存在')
    
    def validate_email(self, field):
        """验证邮箱唯一性"""
        if field.data:
            user = User.query.filter_by(email=field.data).first()
            if user and (not self.user or user.id != self.user.id):
                raise ValidationError('邮箱已存在')

class DeviceGroupForm(FlaskForm):
    """设备分组表单"""
    name = StringField('分组名称', validators=[
        DataRequired(message='分组名称不能为空'),
        Length(min=1, max=64, message='分组名称长度必须在1-64个字符之间'),
        Regexp(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', message='分组名称只能包含字母、数字、下划线和中文')
    ])
    description = TextAreaField('描述', validators=[
        Optional(),
        Length(max=500, message='描述长度不能超过500个字符')
    ])
    
    def __init__(self, group=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.group = group
    
    def validate_name(self, field):
        """验证分组名称唯一性"""
        group = DeviceGroup.query.filter_by(name=field.data).first()
        if group and (not self.group or group.id != self.group.id):
            raise ValidationError('分组名称已存在')

class DeviceForm(FlaskForm):
    """设备表单"""
    name = StringField('设备名称', validators=[
        DataRequired(message='设备名称不能为空'),
        Length(min=1, max=64, message='设备名称长度必须在1-64个字符之间'),
        Regexp(r'^[a-zA-Z0-9_\-]+$', message='设备名称只能包含字母、数字、下划线和连字符')
    ])
    description = TextAreaField('设备描述', validators=[
        Optional(),
        Length(max=500, message='描述长度不能超过500个字符')
    ])
    hardware_version = StringField('硬件版本', validators=[
        Optional(),
        Length(max=32, message='硬件版本长度不能超过32个字符')
    ])
    current_version = StringField('当前版本', validators=[
        DataRequired(message='当前版本不能为空'),
        Length(min=1, max=64, message='版本长度必须在1-64个字符之间')
    ])
    target_version = StringField('目标版本', validators=[
        Optional(),
        Length(max=64, message='目标版本长度不能超过64个字符')
    ])
    group_id = SelectField('设备分组', coerce=int, validators=[Optional()])
    firmware_file = FileField('固件文件', validators=[
        FileAllowed(['bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf'], 
                   message='不支持的文件类型')
    ])
    auto_update = BooleanField('自动更新', default=True)
    is_active = BooleanField('激活状态', default=True)
    
    def __init__(self, device=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.device = device
        
        # 动态加载设备分组选项
        self.group_id.choices = [(0, '无分组')] + [
            (g.id, g.name) for g in DeviceGroup.query.order_by(DeviceGroup.name).all()
        ]
        
        # 编辑模式时，固件文件可选
        if device:
            self.firmware_file.validators = [
                Optional(),
                FileAllowed(['bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf'])
            ]
        else:
            # 新增模式时，固件文件必需
            self.firmware_file.validators.insert(0, FileRequired(message='请选择固件文件'))
    
    def validate_name(self, field):
        """验证设备名称唯一性"""
        device = Device.query.filter_by(name=field.data).first()
        if device and (not self.device or device.id != self.device.id):
            raise ValidationError('设备名称已存在')

class SimulateForm(FlaskForm):
    """模拟查询表单"""
    device = StringField('设备名称', validators=[
        DataRequired(message='设备名称不能为空'),
        Length(min=1, max=64, message='设备名称长度必须在1-64个字符之间')
    ])
    firmware = StringField('当前版本', validators=[
        DataRequired(message='当前版本不能为空'),
        Length(min=1, max=64, message='版本长度必须在1-64个字符之间')
    ])

class ApiKeyForm(FlaskForm):
    """API密钥表单"""
    name = StringField('密钥名称', validators=[
        DataRequired(message='密钥名称不能为空'),
        Length(min=1, max=64, message='密钥名称长度必须在1-64个字符之间')
    ])
    can_read = BooleanField('读取权限', default=True)
    can_write = BooleanField('写入权限')
    can_delete = BooleanField('删除权限')
    allowed_ips = TextAreaField('允许的IP地址', validators=[
        Optional(),
        Length(max=1000, message='IP地址列表长度不能超过1000个字符')
    ], description='每行一个IP地址或CIDR网段，留空表示不限制')
    rate_limit = IntegerField('速率限制(每小时)', validators=[
        Optional(),
        DataRequired(message='速率限制不能为空')
    ], default=100)
    expires_days = IntegerField('有效期(天)', validators=[
        Optional()
    ], description='留空表示永不过期')

class ChangePasswordForm(FlaskForm):
    """修改密码表单"""
    old_password = PasswordField('当前密码', validators=[
        DataRequired(message='当前密码不能为空')
    ])
    new_password = PasswordField('新密码', validators=[
        DataRequired(message='新密码不能为空'),
        Length(min=6, max=128, message='密码长度必须在6-128个字符之间')
    ])
    confirm_password = PasswordField('确认新密码', validators=[
        DataRequired(message='确认密码不能为空')
    ])
    
    def validate_confirm_password(self, field):
        """验证确认密码"""
        if field.data != self.new_password.data:
            raise ValidationError('两次输入的密码不一致')
