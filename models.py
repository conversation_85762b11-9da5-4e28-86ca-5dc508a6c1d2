import hashlib
import os
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import event

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=True, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    is_admin = db.Column(db.<PERSON>, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)
    login_count = db.Column(db.Integer, default=0, nullable=False)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def update_login_info(self):
        """更新登录信息"""
        self.last_login = datetime.utcnow()
        self.login_count += 1
        db.session.commit()
    
    def __repr__(self):
        return f'<User {self.username}>'

class DeviceGroup(db.Model):
    """设备分组模型"""
    __tablename__ = 'device_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 关联设备
    devices = db.relationship('Device', backref='group', lazy='dynamic')
    
    def __repr__(self):
        return f'<DeviceGroup {self.name}>'

class Device(db.Model):
    """设备模型"""
    __tablename__ = 'devices'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    hardware_version = db.Column(db.String(32))
    current_version = db.Column(db.String(64), nullable=False)
    target_version = db.Column(db.String(64))
    
    # 文件信息
    firmware_file = db.Column(db.String(256), nullable=False)
    file_size = db.Column(db.BigInteger)
    file_md5 = db.Column(db.String(32))
    file_sha256 = db.Column(db.String(64))
    
    # 分组关联
    group_id = db.Column(db.Integer, db.ForeignKey('device_groups.id'))
    
    # 状态信息
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    auto_update = db.Column(db.Boolean, default=True, nullable=False)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 统计信息
    download_count = db.Column(db.Integer, default=0, nullable=False)
    last_check_time = db.Column(db.DateTime)
    last_update_time = db.Column(db.DateTime)
    
    # 关联升级历史
    update_histories = db.relationship('UpdateHistory', backref='device', lazy='dynamic', 
                                     cascade='all, delete-orphan')
    
    def calculate_file_hash(self, file_path):
        """计算文件哈希值"""
        if not os.path.exists(file_path):
            return None, None
            
        md5_hash = hashlib.md5()
        sha256_hash = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
                sha256_hash.update(chunk)
        
        return md5_hash.hexdigest(), sha256_hash.hexdigest()
    
    def update_file_info(self, file_path):
        """更新文件信息"""
        if os.path.exists(file_path):
            self.file_size = os.path.getsize(file_path)
            self.file_md5, self.file_sha256 = self.calculate_file_hash(file_path)
    
    def increment_download_count(self):
        """增加下载计数"""
        self.download_count += 1
        self.last_check_time = datetime.utcnow()
        db.session.commit()
    
    def __repr__(self):
        return f'<Device {self.name}>'

class UpdateHistory(db.Model):
    """升级历史模型"""
    __tablename__ = 'update_histories'
    
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    
    # 版本信息
    from_version = db.Column(db.String(64))
    to_version = db.Column(db.String(64), nullable=False)
    
    # 客户端信息
    client_ip = db.Column(db.String(45))  # 支持IPv6
    user_agent = db.Column(db.String(256))
    
    # 状态信息
    status = db.Column(db.String(20), default='requested', nullable=False)  # requested, downloaded, failed
    error_message = db.Column(db.Text)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    completed_at = db.Column(db.DateTime)
    
    def __repr__(self):
        return f'<UpdateHistory {self.device.name}: {self.from_version} -> {self.to_version}>'

class ApiKey(db.Model):
    """API密钥模型"""
    __tablename__ = 'api_keys'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    key_hash = db.Column(db.String(128), unique=True, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # 权限控制
    can_read = db.Column(db.Boolean, default=True, nullable=False)
    can_write = db.Column(db.Boolean, default=False, nullable=False)
    can_delete = db.Column(db.Boolean, default=False, nullable=False)
    
    # 限制条件
    allowed_ips = db.Column(db.Text)  # JSON格式存储IP列表
    rate_limit = db.Column(db.Integer, default=100)  # 每小时请求限制
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_used = db.Column(db.DateTime)
    expires_at = db.Column(db.DateTime)
    
    def set_key(self, key):
        """设置API密钥"""
        self.key_hash = generate_password_hash(key)
    
    def check_key(self, key):
        """验证API密钥"""
        return check_password_hash(self.key_hash, key)
    
    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used = datetime.utcnow()
        db.session.commit()
    
    def __repr__(self):
        return f'<ApiKey {self.name}>'

# 数据库事件监听器
@event.listens_for(Device.firmware_file, 'set')
def update_file_info_on_change(target, value, oldvalue, initiator):
    """当固件文件路径改变时，自动更新文件信息"""
    if value and value != oldvalue:
        from flask import current_app
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], value)
        target.update_file_info(file_path)
