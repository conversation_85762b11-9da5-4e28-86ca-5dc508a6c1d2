import os
import uuid
import hashlib
import secrets
import ipaddress
import json
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, abort
from flask_login import current_user
from werkzeug.utils import secure_filename
from models import A<PERSON><PERSON><PERSON>, UpdateHistory, db

def allowed_file(filename, allowed_extensions=None):
    """检查文件扩展名是否允许"""
    if allowed_extensions is None:
        allowed_extensions = current_app.config['ALLOWED_EXTENSIONS']
    
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in allowed_extensions)

def generate_unique_filename(original_filename, device_name=None):
    """生成唯一的文件名"""
    if not original_filename:
        return None
    
    # 获取文件扩展名
    ext = ''
    if '.' in original_filename:
        ext = '.' + original_filename.rsplit('.', 1)[1].lower()
    
    # 生成唯一标识符
    unique_id = uuid.uuid4().hex[:8]
    
    # 构建文件名
    if device_name:
        # 使用设备名作为前缀
        safe_device_name = secure_filename(device_name)
        filename = f"{safe_device_name}_{unique_id}{ext}"
    else:
        filename = f"firmware_{unique_id}{ext}"
    
    return filename

def calculate_file_hash(file_path, algorithms=None):
    """计算文件哈希值"""
    if algorithms is None:
        algorithms = ['md5', 'sha256']
    
    if not os.path.exists(file_path):
        return {alg: None for alg in algorithms}
    
    hashers = {}
    for alg in algorithms:
        if alg == 'md5':
            hashers[alg] = hashlib.md5()
        elif alg == 'sha256':
            hashers[alg] = hashlib.sha256()
        elif alg == 'sha1':
            hashers[alg] = hashlib.sha1()
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(8192), b""):
            for hasher in hashers.values():
                hasher.update(chunk)
    
    return {alg: hasher.hexdigest() for alg, hasher in hashers.items()}

def get_file_size(file_path):
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0

def format_file_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def validate_ip_address(ip_str):
    """验证IP地址格式"""
    try:
        ipaddress.ip_address(ip_str)
        return True
    except ValueError:
        try:
            ipaddress.ip_network(ip_str, strict=False)
            return True
        except ValueError:
            return False

def is_ip_allowed(client_ip, allowed_ips_json):
    """检查IP是否在允许列表中"""
    if not allowed_ips_json:
        return True
    
    try:
        allowed_ips = json.loads(allowed_ips_json)
        if not allowed_ips:
            return True
        
        client_addr = ipaddress.ip_address(client_ip)
        
        for allowed_ip in allowed_ips:
            try:
                if '/' in allowed_ip:
                    # CIDR网段
                    if client_addr in ipaddress.ip_network(allowed_ip, strict=False):
                        return True
                else:
                    # 单个IP
                    if client_addr == ipaddress.ip_address(allowed_ip):
                        return True
            except ValueError:
                continue
        
        return False
    except (json.JSONDecodeError, ValueError):
        return True  # 配置错误时允许访问

def generate_api_key():
    """生成API密钥"""
    return secrets.token_urlsafe(32)

def get_client_ip():
    """获取客户端IP地址"""
    # 检查代理头
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def log_update_request(device, from_version=None, status='requested', error_message=None):
    """记录升级请求"""
    try:
        history = UpdateHistory(
            device_id=device.id,
            from_version=from_version,
            to_version=device.current_version,
            client_ip=get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            status=status,
            error_message=error_message
        )
        
        if status in ['downloaded', 'failed']:
            history.completed_at = datetime.utcnow()
        
        db.session.add(history)
        db.session.commit()
        
        return history
    except Exception as e:
        current_app.logger.error(f"Failed to log update request: {e}")
        return None

def require_api_key(permissions=None):
    """API密钥认证装饰器"""
    if permissions is None:
        permissions = ['read']
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 从请求头获取API密钥
            api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
            
            if not api_key:
                return jsonify({'error': 'API key required'}), 401
            
            # 验证API密钥
            api_key_obj = None
            for key_obj in ApiKey.query.filter_by(is_active=True).all():
                if key_obj.check_key(api_key):
                    api_key_obj = key_obj
                    break
            
            if not api_key_obj:
                return jsonify({'error': 'Invalid API key'}), 401
            
            # 检查过期时间
            if api_key_obj.expires_at and api_key_obj.expires_at < datetime.utcnow():
                return jsonify({'error': 'API key expired'}), 401
            
            # 检查IP限制
            client_ip = get_client_ip()
            if not is_ip_allowed(client_ip, api_key_obj.allowed_ips):
                return jsonify({'error': 'IP not allowed'}), 403
            
            # 检查权限
            for permission in permissions:
                if permission == 'read' and not api_key_obj.can_read:
                    return jsonify({'error': 'Read permission required'}), 403
                elif permission == 'write' and not api_key_obj.can_write:
                    return jsonify({'error': 'Write permission required'}), 403
                elif permission == 'delete' and not api_key_obj.can_delete:
                    return jsonify({'error': 'Delete permission required'}), 403
            
            # 更新最后使用时间
            api_key_obj.update_last_used()
            
            # 将API密钥对象添加到请求上下文
            request.api_key = api_key_obj
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def version_compare(version1, version2):
    """版本号比较
    
    返回值:
    -1: version1 < version2
     0: version1 == version2
     1: version1 > version2
    """
    def normalize_version(v):
        """标准化版本号"""
        # 移除前缀字母和特殊字符，只保留数字和点
        import re
        normalized = re.sub(r'[^\d.]', '', str(v))
        parts = [int(x) for x in normalized.split('.') if x.isdigit()]
        return parts
    
    try:
        v1_parts = normalize_version(version1)
        v2_parts = normalize_version(version2)
        
        # 补齐长度
        max_len = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (max_len - len(v1_parts)))
        v2_parts.extend([0] * (max_len - len(v2_parts)))
        
        # 逐位比较
        for i in range(max_len):
            if v1_parts[i] < v2_parts[i]:
                return -1
            elif v1_parts[i] > v2_parts[i]:
                return 1
        
        return 0
    except (ValueError, IndexError):
        # 如果无法解析版本号，则按字符串比较
        if version1 < version2:
            return -1
        elif version1 > version2:
            return 1
        else:
            return 0

def cleanup_old_files(upload_folder, max_age_days=30):
    """清理旧文件"""
    try:
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        
        for filename in os.listdir(upload_folder):
            file_path = os.path.join(upload_folder, filename)
            
            if os.path.isfile(file_path):
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                if file_mtime < cutoff_time:
                    # 检查文件是否仍在使用
                    from models import Device
                    if not Device.query.filter_by(firmware_file=filename).first():
                        os.remove(file_path)
                        current_app.logger.info(f"Cleaned up old file: {filename}")
    
    except Exception as e:
        current_app.logger.error(f"Failed to cleanup old files: {e}")

def get_storage_stats(upload_folder):
    """获取存储统计信息"""
    try:
        total_size = 0
        file_count = 0
        
        for filename in os.listdir(upload_folder):
            file_path = os.path.join(upload_folder, filename)
            if os.path.isfile(file_path):
                total_size += os.path.getsize(file_path)
                file_count += 1
        
        return {
            'total_size': total_size,
            'total_size_formatted': format_file_size(total_size),
            'file_count': file_count
        }
    except Exception as e:
        current_app.logger.error(f"Failed to get storage stats: {e}")
        return {
            'total_size': 0,
            'total_size_formatted': '0 B',
            'file_count': 0
        }
