# OTA服务器使用说明

## 📖 概述

这是一个轻量级的OTA（Over-The-Air）固件更新服务器，支持设备管理、文件上传下载和下载记录功能。

## 🚀 快速开始

### 1. 访问管理界面

打开浏览器访问: `http://ota.tyw.com`

### 2. 登录系统

- **用户名**: `admin`
- **密码**: `123456`

⚠️ **重要**: 首次使用后请立即修改默认密码！

## 📱 设备管理

### 添加新设备

1. 点击 **"添加设备"** 按钮
2. 填写设备信息：
   - **设备名称**: 唯一标识符（如：device001、router_v1等）
   - **版本号**: 当前固件版本（如：v1.0.0、2.1.3等）
   - **设备描述**: 可选，设备的详细说明
   - **固件文件**: 选择要上传的固件文件
   - **启用设备**: 勾选后设备才能获取更新

3. 点击 **"创建设备"** 完成添加

### 编辑设备

1. 在设备列表中点击 **编辑** 按钮
2. 修改需要的信息
3. 如需更换固件文件，重新选择文件
4. 点击 **"更新设备"** 保存修改

### 删除设备

1. 在设备列表中点击 **删除** 按钮
2. 确认删除操作
3. ⚠️ **注意**: 删除设备会同时删除关联的固件文件，且无法恢复

## 📁 文件上传

### 支持的文件格式

- `.bin` - 二进制固件文件
- `.img` - 镜像文件
- `.zip` - 压缩包
- `.tar`, `.gz`, `.bz2`, `.xz` - 各种压缩格式
- `.hex` - Intel HEX格式
- `.elf` - 可执行文件格式
- `.apk` - Android应用包

### 上传功能特性

- **大文件支持**: 最大支持1GB文件上传
- **进度显示**: 实时显示上传进度
- **取消上传**: 可随时取消正在进行的上传
- **自动校验**: 自动计算MD5校验值

### 上传注意事项

1. 确保网络连接稳定
2. 大文件上传可能需要较长时间，请耐心等待
3. 上传过程中不要关闭浏览器
4. 如果上传失败，可以重试

## 🔍 模拟测试

### 测试OTA更新

1. 访问 **"模拟测试"** 页面
2. 输入测试参数：
   - **设备名称**: 要测试的设备名称
   - **当前版本**: 模拟设备的当前版本号
3. 点击 **"执行测试"** 查看结果

### 测试结果说明

- **需要更新**: 当前版本低于服务器版本
- **无需更新**: 当前版本等于或高于服务器版本
- **设备不存在**: 输入的设备名称不存在

## 🔧 API接口使用

### 检查更新接口

**请求地址**: `POST /api/update`

**请求参数**:
```
device=设备名称&firmware=当前版本
```

**响应示例**:
```json
{
  "update": true,
  "latest_version": "v2.1.0",
  "url": "http://ota.tyw.com/ota/firmware.bin",
  "file_size": 1048576,
  "md5": "d41d8cd98f00b204e9800998ecf8427e"
}
```

### 客户端示例代码

#### curl命令
```bash
curl -X POST http://ota.tyw.com/api/update \
  -d "device=your_device_name" \
  -d "firmware=your_current_version"
```

#### Python示例
```python
import requests

response = requests.post('http://ota.tyw.com/api/update', {
    'device': 'your_device_name',
    'firmware': 'your_current_version'
})

result = response.json()
if result.get('update'):
    print(f"Update available: {result['url']}")
    print(f"New version: {result['latest_version']}")
    print(f"File size: {result['file_size']} bytes")
    print(f"MD5: {result['md5']}")
else:
    print("No update needed")
```

## 📊 版本比较规则

### 版本号格式

支持多种版本号格式：
- `v1.0.0`, `v2.1.3`
- `1.0`, `2.1`
- `1.0.0.1`, `2.1.3.4`

### 比较逻辑

1. **数字比较**: 优先按数字大小比较
2. **分段比较**: 按点号分割，逐段比较
3. **补零处理**: 短版本号自动补零
4. **示例**:
   - `v1.0` < `v1.1` ✅
   - `v1.9` < `v2.0` ✅
   - `v1.0.1` > `v1.0` ✅
   - `v2.0` > `v1.9` ✅

## 📈 统计信息

### 设备统计

- **总设备数**: 系统中的设备总数
- **存储使用**: 固件文件占用的存储空间
- **文件数量**: 上传的固件文件总数

### 下载记录

- **下载次数**: 每个设备的下载统计
- **下载历史**: 详细的下载记录，包括IP地址和时间
- **最后检查**: 设备最后一次检查更新的时间

## ⚠️ 注意事项

### 安全建议

1. **修改默认密码**: 首次使用后立即修改管理员密码
2. **网络安全**: 建议在内网环境使用，或配置防火墙
3. **备份数据**: 定期备份数据库文件

### 性能优化

1. **nginx托管**: 使用nginx直接托管固件文件，提高下载性能
2. **定期清理**: 删除不再使用的固件文件
3. **监控存储**: 注意磁盘空间使用情况

### 故障排除

1. **上传失败**: 检查文件大小和网络连接
2. **下载失败**: 检查nginx配置和文件权限
3. **版本比较异常**: 确保版本号格式正确

## 📞 技术支持

如遇到问题，请检查：

1. **日志文件**: 查看应用日志了解详细错误信息
2. **nginx日志**: 检查nginx访问和错误日志
3. **系统资源**: 确保磁盘空间和内存充足
4. **网络连接**: 确保服务器网络正常

## 📝 更新日志

### v1.0.0
- 基础OTA功能
- 设备管理
- 文件上传下载
- 版本比较优化
- 上传进度条
- 使用说明文档
