import os
import logging
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, abort, send_from_directory
from flask_login import LoginManager, login_user, login_required, logout_user, current_user
from flask_migrate import Migrate
from werkzeug.utils import secure_filename

# 导入配置和模型
from config import config
from models import db, User, Device, DeviceGroup, UpdateHistory, ApiKey
from forms import LoginForm, DeviceForm, DeviceGroupForm, SimulateForm, UserForm, ApiKeyForm, ChangePasswordForm
from utils import (
    allowed_file, generate_unique_filename, calculate_file_hash, get_file_size,
    format_file_size, log_update_request, require_api_key, admin_required,
    version_compare, get_client_ip, cleanup_old_files, get_storage_stats
)

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 初始化扩展
    db.init_app(app)
    migrate = Migrate(app, db)

    # 初始化配置
    config[config_name].init_app(app)

    return app

# 创建应用实例
app = create_app()

# 初始化数据库迁移
migrate = Migrate(app, db)

# Flask-Login 配置
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'warning'

@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    return User.query.get(int(user_id))

# 数据库初始化
def init_db():
    """初始化数据库和默认数据"""
    with app.app_context():
        # 创建所有表
        db.create_all()

        # 创建默认管理员账号
        admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
        admin_password = os.environ.get('ADMIN_PASSWORD', '123456')

        admin = User.query.filter_by(username=admin_username).first()
        if not admin:
            admin = User(
                username=admin_username,
                is_admin=True,
                is_active=True
            )
            admin.set_password(admin_password)
            db.session.add(admin)

            # 创建默认设备分组
            default_group = DeviceGroup(
                name='默认分组',
                description='系统默认设备分组'
            )
            db.session.add(default_group)

            db.session.commit()
            app.logger.info(f'Created default admin user: {admin_username}')

# ============ 认证路由 ============

@app.route('/')
def index():
    """首页重定向"""
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.is_active and user.check_password(form.password.data):
            login_user(user, remember=form.remember_me.data)
            user.update_login_info()
            flash('登录成功', 'success')

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('admin_list')
            return redirect(next_page)
        else:
            flash('用户名或密码错误，或账号已被禁用', 'danger')

    return render_template('auth/login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('已登出', 'info')
    return redirect(url_for('login'))

# ============ OTA API 接口 ============

@app.route('/api/update', methods=['GET', 'POST'])
def api_update():
    """现代OTA更新检查接口"""
    device_name = request.values.get('device')
    current_version = request.values.get('firmware') or request.values.get('version')

    if not device_name:
        return jsonify({'error': 'Device name required'}), 400

    if not current_version:
        return jsonify({'error': 'Current version required'}), 400

    # 查找设备
    device = Device.query.filter_by(name=device_name, is_active=True).first()
    if not device:
        log_update_request(None, current_version, 'failed', 'Unknown device')
        return jsonify({
            'update': False,
            'reason': 'unknown device',
            'message': f'Device "{device_name}" not found'
        }), 404

    # 更新设备检查时间
    device.last_check_time = datetime.utcnow()

    # 版本比较
    target_version = device.target_version or device.current_version
    version_cmp = version_compare(current_version, target_version)

    if version_cmp < 0:  # 当前版本低于目标版本，需要更新
        # 构建下载URL
        download_url = app.config['DOWNLOAD_BASE_URL'] + device.firmware_file

        # 记录更新请求
        log_update_request(device, current_version, 'requested')

        # 增加下载计数
        device.increment_download_count()

        response_data = {
            'update': True,
            'latest_version': target_version,
            'url': download_url,
            'file_size': device.file_size,
            'md5': device.file_md5,
            'sha256': device.file_sha256
        }

        # 如果有硬件版本要求，添加到响应中
        if device.hardware_version:
            response_data['hardware_version'] = device.hardware_version

        return jsonify(response_data)

    else:  # 已是最新版本或版本更高
        db.session.commit()  # 提交检查时间更新
        return jsonify({
            'update': False,
            'message': 'Already latest version',
            'current_version': target_version
        })

@app.route('/update', methods=['GET', 'POST'])
def allwinner_ota_update():
    """全志OTA协议兼容接口"""
    try:
        # 解析客户端信息
        client_info = {}

        if request.method == 'POST':
            # 尝试解析XML格式的POST数据
            if request.content_type and 'xml' in request.content_type:
                import xml.etree.ElementTree as ET
                try:
                    root = ET.fromstring(request.data)
                    for child in root:
                        client_info[child.tag] = child.text
                except ET.ParseError:
                    pass

            # 如果不是XML，尝试表单数据
            if not client_info:
                client_info = request.form.to_dict()
        else:
            # GET请求参数
            client_info = request.args.to_dict()

        # 提取关键信息
        device_name = client_info.get('device')
        firmware_version = client_info.get('firmware')
        android_version = client_info.get('android')
        board = client_info.get('board')
        brand = client_info.get('brand')
        fingerprint = client_info.get('fingerprint')
        client_id = client_info.get('id', '0')

        app.logger.info(f"Allwinner OTA request: device={device_name}, firmware={firmware_version}, id={client_id}")

        if not device_name or not firmware_version:
            return '', 200  # 全志协议：信息不匹配返回空

        # 查找设备配置
        device = Device.query.filter_by(name=device_name, is_active=True).first()
        if not device:
            app.logger.warning(f"Unknown device: {device_name}")
            return '', 200  # 全志协议：设备不存在返回空

        # 更新设备检查信息
        device.last_check_time = datetime.utcnow()

        # 记录客户端信息到升级历史
        history = UpdateHistory(
            device_id=device.id,
            from_version=firmware_version,
            to_version=device.current_version,
            client_ip=get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            status='requested'
        )
        db.session.add(history)

        # 版本比较
        target_version = device.target_version or device.current_version

        if version_compare(firmware_version, target_version) < 0:
            # 需要更新，返回XML文件URL
            xml_url = f"{request.url_root}ota/xml/{device.name}.xml"
            device.increment_download_count()

            app.logger.info(f"Update available for {device_name}: {firmware_version} -> {target_version}")
            return f"url={xml_url}", 200
        else:
            # 已是最新版本
            db.session.commit()
            app.logger.info(f"Device {device_name} is up to date: {firmware_version}")
            return 'url=null', 200

    except Exception as e:
        app.logger.error(f"Allwinner OTA error: {e}")
        return '', 200  # 出错时返回空

@app.route('/ota/xml/<device_name>.xml')
def get_device_xml(device_name):
    """生成设备的XML更新文件"""
    device = Device.query.filter_by(name=device_name, is_active=True).first()
    if not device:
        abort(404)

    # 构建下载URL
    download_url = app.config['DOWNLOAD_BASE_URL'] + device.firmware_file
    target_version = device.target_version or device.current_version

    # 生成XML内容
    xml_content = f'''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<updata>
    <command force="false" name="update_with_inc_ota">
        <url>{download_url}</url>
        <md5>{device.file_md5 or ''}</md5>
        <description country="CN" language="zh">{device.description or f'升级到版本 {target_version}'}</description>
        <description country="ELSE" language="en">{device.description or f'Update to version {target_version}'}</description>
    </command>
</updata>'''

    return xml_content, 200, {'Content-Type': 'application/xml; charset=utf-8'}

# ============ 管理界面路由 ============

@app.route('/admin')
@login_required
def admin_list():
    """设备列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 搜索和过滤
    search = request.args.get('search', '')
    group_id = request.args.get('group_id', type=int)

    query = Device.query

    if search:
        query = query.filter(Device.name.contains(search))

    if group_id:
        query = query.filter(Device.group_id == group_id)

    devices = query.order_by(Device.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取设备分组
    groups = DeviceGroup.query.order_by(DeviceGroup.name).all()

    # 获取存储统计
    storage_stats = get_storage_stats(app.config['UPLOAD_FOLDER'])

    return render_template('admin/device_list.html',
                         devices=devices,
                         groups=groups,
                         storage_stats=storage_stats,
                         search=search,
                         current_group_id=group_id)

@app.route('/admin/device/new', methods=['GET', 'POST'])
@login_required
def admin_device_new():
    """新增设备"""
    form = DeviceForm()

    if form.validate_on_submit():
        # 处理文件上传
        firmware_file = form.firmware_file.data
        if firmware_file and allowed_file(firmware_file.filename):
            filename = generate_unique_filename(firmware_file.filename, form.name.data)
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

            # 确保上传目录存在
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
            firmware_file.save(file_path)

            # 创建设备记录
            device = Device(
                name=form.name.data,
                description=form.description.data,
                hardware_version=form.hardware_version.data,
                current_version=form.current_version.data,
                target_version=form.target_version.data,
                firmware_file=filename,
                group_id=form.group_id.data if form.group_id.data else None,
                auto_update=form.auto_update.data,
                is_active=form.is_active.data
            )

            # 计算文件信息
            device.update_file_info(file_path)

            db.session.add(device)
            db.session.commit()

            flash(f'设备 "{device.name}" 创建成功', 'success')
            return redirect(url_for('admin_list'))
        else:
            flash('请选择有效的固件文件', 'danger')

    return render_template('admin/device_form.html', form=form, title='新增设备')

@app.route('/admin/device/<int:device_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_device_edit(device_id):
    """编辑设备"""
    device = Device.query.get_or_404(device_id)
    form = DeviceForm(device=device, obj=device)

    if form.validate_on_submit():
        # 更新基本信息
        device.name = form.name.data
        device.description = form.description.data
        device.hardware_version = form.hardware_version.data
        device.current_version = form.current_version.data
        device.target_version = form.target_version.data
        device.group_id = form.group_id.data if form.group_id.data else None
        device.auto_update = form.auto_update.data
        device.is_active = form.is_active.data

        # 处理文件上传
        firmware_file = form.firmware_file.data
        if firmware_file and allowed_file(firmware_file.filename):
            # 删除旧文件
            old_file_path = os.path.join(app.config['UPLOAD_FOLDER'], device.firmware_file)
            if os.path.exists(old_file_path):
                os.remove(old_file_path)

            # 保存新文件
            filename = generate_unique_filename(firmware_file.filename, device.name)
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            firmware_file.save(file_path)

            device.firmware_file = filename
            device.update_file_info(file_path)

        db.session.commit()
        flash(f'设备 "{device.name}" 更新成功', 'success')
        return redirect(url_for('admin_list'))

    return render_template('admin/device_form.html', form=form, device=device, title='编辑设备')

@app.route('/admin/device/<int:device_id>/delete', methods=['POST'])
@login_required
def admin_device_delete(device_id):
    """删除设备"""
    device = Device.query.get_or_404(device_id)

    # 删除关联的固件文件
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], device.firmware_file)
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
        except OSError as e:
            app.logger.warning(f"Failed to delete file {file_path}: {e}")

    device_name = device.name
    db.session.delete(device)
    db.session.commit()

    flash(f'设备 "{device_name}" 删除成功', 'success')
    return redirect(url_for('admin_list'))

@app.route('/admin/groups')
@login_required
def admin_groups():
    """设备分组管理"""
    groups = DeviceGroup.query.order_by(DeviceGroup.name).all()
    return render_template('admin/group_list.html', groups=groups)

@app.route('/admin/group/new', methods=['GET', 'POST'])
@login_required
def admin_group_new():
    """新增设备分组"""
    form = DeviceGroupForm()

    if form.validate_on_submit():
        group = DeviceGroup(
            name=form.name.data,
            description=form.description.data
        )
        db.session.add(group)
        db.session.commit()

        flash(f'分组 "{group.name}" 创建成功', 'success')
        return redirect(url_for('admin_groups'))

    return render_template('admin/group_form.html', form=form, title='新增分组')

@app.route('/admin/group/<int:group_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_group_edit(group_id):
    """编辑设备分组"""
    group = DeviceGroup.query.get_or_404(group_id)
    form = DeviceGroupForm(group=group, obj=group)

    if form.validate_on_submit():
        group.name = form.name.data
        group.description = form.description.data
        db.session.commit()

        flash(f'分组 "{group.name}" 更新成功', 'success')
        return redirect(url_for('admin_groups'))

    return render_template('admin/group_form.html', form=form, group=group, title='编辑分组')

@app.route('/admin/group/<int:group_id>/delete', methods=['POST'])
@login_required
def admin_group_delete(group_id):
    """删除设备分组"""
    group = DeviceGroup.query.get_or_404(group_id)

    # 检查是否有设备使用此分组
    if group.devices.count() > 0:
        flash(f'分组 "{group.name}" 下还有设备，无法删除', 'danger')
        return redirect(url_for('admin_groups'))

    group_name = group.name
    db.session.delete(group)
    db.session.commit()

    flash(f'分组 "{group_name}" 删除成功', 'success')
    return redirect(url_for('admin_groups'))

@app.route('/simulate', methods=['GET', 'POST'])
@login_required
def simulate():
    """模拟OTA查询"""
    form = SimulateForm()
    result = None

    if form.validate_on_submit():
        # 模拟API调用
        with app.test_request_context(
            '/api/update',
            method='POST',
            data={
                'device': form.device.data,
                'firmware': form.firmware.data
            }
        ):
            response = api_update()
            if hasattr(response, 'get_json'):
                result = response.get_json()
            else:
                result = response[0].get_json() if isinstance(response, tuple) else response

    return render_template('admin/simulate.html', form=form, result=result)

# ============ 静态文件服务 ============

@app.route('/static/ota/<filename>')
def download_firmware(filename):
    """固件文件下载"""
    # 记录下载
    device = Device.query.filter_by(firmware_file=filename).first()
    if device:
        device.increment_download_count()
        log_update_request(device, status='downloaded')

    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# ============ 错误处理 ============

@app.errorhandler(401)
def unauthorized(error):
    """401错误处理"""
    return render_template('errors/401.html'), 401

@app.errorhandler(403)
def forbidden(error):
    """403错误处理"""
    return render_template('errors/403.html'), 403

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    db.session.rollback()
    return render_template('errors/500.html'), 500

# ============ 应用启动 ============

if __name__ == '__main__':
    # 初始化数据库
    init_db()

    # 启动应用
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    app.run(host='0.0.0.0', port=port, debug=debug)

