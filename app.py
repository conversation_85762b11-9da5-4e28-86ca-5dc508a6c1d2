import os
import uuid
import hashlib
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>anager, login_user, login_required, logout_user, current_user, UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename

# 配置
UPLOAD_FOLDER = os.path.join('static', 'ota')
ALLOWED_EXTENSIONS = {'bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf', 'apk'}
DOWNLOAD_BASE_URL = os.environ.get('DOWNLOAD_BASE_URL', 'http://ota.tyw.com/ota/')
DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///ota_config.db')
SECRET_KEY = os.environ.get('SECRET_KEY', 'change-this-secret-key')

# Flask应用
app = Flask(__name__)
app.config.update({
    'UPLOAD_FOLDER': UPLOAD_FOLDER,
    'SQLALCHEMY_DATABASE_URI': DATABASE_URI,
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SECRET_KEY': SECRET_KEY,
    'MAX_CONTENT_LENGTH': 500 * 1024 * 1024,  # 500MB
})

# 初始化数据库
db = SQLAlchemy(app)

# 数据库模型
class User(UserMixin, db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    is_admin = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Device(db.Model):
    __tablename__ = 'devices'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.Text)
    current_version = db.Column(db.String(64), nullable=False)
    firmware_file = db.Column(db.String(256), nullable=False)
    file_size = db.Column(db.BigInteger)
    file_md5 = db.Column(db.String(32))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    download_count = db.Column(db.Integer, default=0, nullable=False)
    last_check_time = db.Column(db.DateTime)

class DownloadLog(db.Model):
    __tablename__ = 'download_logs'
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    client_ip = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    download_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    device = db.relationship('Device', backref='download_logs')

# Flask-Login 配置
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'warning'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 工具函数
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename, device_name=None):
    if not original_filename:
        return None

    ext = ''
    if '.' in original_filename:
        ext = '.' + original_filename.rsplit('.', 1)[1].lower()

    unique_id = uuid.uuid4().hex[:8]

    if device_name:
        safe_device_name = secure_filename(device_name)
        filename = f"{safe_device_name}_{unique_id}{ext}"
    else:
        filename = f"firmware_{unique_id}{ext}"

    return filename

def calculate_file_md5(file_path):
    if not os.path.exists(file_path):
        return None

    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_client_ip():
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def log_download(device, client_ip, user_agent):
    log = DownloadLog(
        device_id=device.id,
        client_ip=client_ip,
        user_agent=user_agent
    )
    db.session.add(log)
    device.download_count += 1
    device.last_check_time = datetime.utcnow()
    db.session.commit()

# 数据库初始化
def init_db():
    with app.app_context():
        db.create_all()

        admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
        admin_password = os.environ.get('ADMIN_PASSWORD', '123456')

        admin = User.query.filter_by(username=admin_username).first()
        if not admin:
            admin = User(username=admin_username, is_admin=True)
            admin.set_password(admin_password)
            db.session.add(admin)
            db.session.commit()
            print(f'Created admin user: {admin_username}')

# ============ 路由 ============

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('登录成功', 'success')
            return redirect(request.args.get('next') or url_for('admin_list'))
        else:
            flash('用户名或密码错误', 'danger')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('已登出', 'info')
    return redirect(url_for('login'))

# ============ OTA API 接口 ============

@app.route('/api/update', methods=['GET', 'POST'])
def api_update():
    """OTA更新检查接口"""
    device_name = request.values.get('device')
    current_version = request.values.get('firmware') or request.values.get('version')

    if not device_name or not current_version:
        return jsonify({'update': False, 'reason': 'missing parameters'})

    # 查找设备
    device = Device.query.filter_by(name=device_name, is_active=True).first()
    if not device:
        return jsonify({'update': False, 'reason': 'unknown device'})

    # 更新检查时间
    device.last_check_time = datetime.utcnow()

    # 简单版本比较（字符串比较）
    if current_version != device.current_version:
        # 构建下载URL
        download_url = DOWNLOAD_BASE_URL + device.firmware_file

        # 记录下载日志
        log_download(device, get_client_ip(), request.headers.get('User-Agent', ''))

        return jsonify({
            'update': True,
            'latest_version': device.current_version,
            'url': download_url,
            'file_size': device.file_size,
            'md5': device.file_md5
        })
    else:
        db.session.commit()
        return jsonify({'update': False, 'message': 'Already latest version'})

# 兼容旧版本API
@app.route('/update', methods=['GET', 'POST'])
def update():
    """兼容旧版本的更新接口"""
    return api_update()

# ============ 管理界面路由 ============

@app.route('/admin')
@login_required
def admin_list():
    """设备列表页面"""
    devices = Device.query.order_by(Device.updated_at.desc()).all()

    # 计算存储统计
    total_size = 0
    file_count = len(devices)
    for device in devices:
        if device.file_size:
            total_size += device.file_size

    storage_stats = {
        'total_size': total_size,
        'total_size_formatted': f"{total_size / 1024 / 1024:.1f} MB" if total_size > 0 else "0 MB",
        'file_count': file_count
    }

    return render_template('device_list.html',
                         devices=devices,
                         storage_stats=storage_stats)

@app.route('/admin/device/new', methods=['GET', 'POST'])
@login_required
def admin_device_new():
    """新增设备"""
    if request.method == 'POST':
        name = request.form['name'].strip()
        description = request.form.get('description', '').strip()
        version = request.form['version'].strip()
        firmware_file = request.files.get('firmware')

        if not name or not version:
            flash('设备名称和版本号不能为空', 'danger')
            return render_template('device_form.html')

        if Device.query.filter_by(name=name).first():
            flash('设备名称已存在', 'danger')
            return render_template('device_form.html')

        if firmware_file and firmware_file.filename and allowed_file(firmware_file.filename):
            filename = generate_unique_filename(firmware_file.filename, name)
            file_path = os.path.join(UPLOAD_FOLDER, filename)

            # 确保上传目录存在
            os.makedirs(UPLOAD_FOLDER, exist_ok=True)
            firmware_file.save(file_path)

            # 计算文件信息
            file_size = os.path.getsize(file_path)
            file_md5 = calculate_file_md5(file_path)

            # 创建设备记录
            device = Device(
                name=name,
                description=description,
                current_version=version,
                firmware_file=filename,
                file_size=file_size,
                file_md5=file_md5,
                is_active=True
            )

            db.session.add(device)
            db.session.commit()

            flash(f'设备 "{device.name}" 创建成功', 'success')
            return redirect(url_for('admin_list'))
        else:
            flash('请选择有效的固件文件', 'danger')

    return render_template('device_form.html', title='新增设备')

@app.route('/admin/device/<int:device_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_device_edit(device_id):
    """编辑设备"""
    device = Device.query.get_or_404(device_id)

    if request.method == 'POST':
        name = request.form['name'].strip()
        description = request.form.get('description', '').strip()
        version = request.form['version'].strip()
        firmware_file = request.files.get('firmware')
        is_active = 'is_active' in request.form

        if not name or not version:
            flash('设备名称和版本号不能为空', 'danger')
            return render_template('device_form.html', device=device)

        # 检查名称唯一性（排除自己）
        existing = Device.query.filter_by(name=name).first()
        if existing and existing.id != device.id:
            flash('设备名称已存在', 'danger')
            return render_template('device_form.html', device=device)

        # 更新基本信息
        device.name = name
        device.description = description
        device.current_version = version
        device.is_active = is_active

        # 处理文件上传
        if firmware_file and firmware_file.filename and allowed_file(firmware_file.filename):
            # 删除旧文件
            old_file_path = os.path.join(UPLOAD_FOLDER, device.firmware_file)
            if os.path.exists(old_file_path):
                os.remove(old_file_path)

            # 保存新文件
            filename = generate_unique_filename(firmware_file.filename, device.name)
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            firmware_file.save(file_path)

            # 更新文件信息
            device.firmware_file = filename
            device.file_size = os.path.getsize(file_path)
            device.file_md5 = calculate_file_md5(file_path)

        db.session.commit()
        flash(f'设备 "{device.name}" 更新成功', 'success')
        return redirect(url_for('admin_list'))

    return render_template('device_form.html', device=device, title='编辑设备')

@app.route('/admin/device/<int:device_id>/delete', methods=['POST'])
@login_required
def admin_device_delete(device_id):
    """删除设备"""
    device = Device.query.get_or_404(device_id)

    # 删除关联的固件文件
    file_path = os.path.join(UPLOAD_FOLDER, device.firmware_file)
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
        except OSError:
            pass  # 忽略文件删除错误

    device_name = device.name
    db.session.delete(device)
    db.session.commit()

    flash(f'设备 "{device_name}" 删除成功', 'success')
    return redirect(url_for('admin_list'))

@app.route('/simulate', methods=['GET', 'POST'])
@login_required
def simulate():
    """模拟OTA查询"""
    result = None

    if request.method == 'POST':
        device_name = request.form.get('device')
        firmware_version = request.form.get('firmware')

        if device_name and firmware_version:
            # 模拟API调用
            with app.test_request_context(
                '/api/update',
                method='POST',
                data={
                    'device': device_name,
                    'firmware': firmware_version
                }
            ):
                response = api_update()
                if hasattr(response, 'get_json'):
                    result = response.get_json()

    return render_template('simulate.html', result=result)

# ============ 静态文件服务 ============

@app.route('/ota/<filename>')
def download_firmware(filename):
    """固件文件下载"""
    # 记录下载
    device = Device.query.filter_by(firmware_file=filename).first()
    if device:
        log_download(device, get_client_ip(), request.headers.get('User-Agent', ''))

    return send_from_directory(UPLOAD_FOLDER, filename)

# ============ 应用启动 ============

if __name__ == '__main__':
    # 初始化数据库
    init_db()

    # 启动应用
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    app.run(host='0.0.0.0', port=port, debug=debug)

