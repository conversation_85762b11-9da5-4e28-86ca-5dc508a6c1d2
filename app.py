import os
import uuid
from flask import (
    Flask, request, jsonify, abort,
    render_template, redirect, url_for, flash
)
from werkzeug.utils import secure_filename
from flask_sqlalchemy import SQLAlchemy
from flask_login import (
    LoginManager, login_user, login_required,
    logout_user, current_user, UserMixin
)
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import inspect

# 配置项
UPLOAD_FOLDER = os.path.join('static', 'ota')
ALLOWED_EXTENSIONS = {'bin', 'img', 'zip', 'tar'}
DOWNLOAD_BASE_URL = 'http://*************/ota/'
DATABASE_URI = 'sqlite:///ota_config.db'
SECRET_KEY = 'ChangeThisToAStrongRandomValue'

# Flask 应用
app = Flask(__name__)
app.config.update({
    'UPLOAD_FOLDER': UPLOAD_FOLDER,
    'SQLALCHEMY_DATABASE_URI': DATABASE_URI,
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SECRET_KEY': SECRET_KEY,
})

# 初始化数据库
db = SQLAlchemy(app)

# Flask-Login 配置
login_manager = LoginManager(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'warning'

# 模型定义
class Device(db.Model):
    __tablename__ = 'devices'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    version = db.Column(db.String(64), nullable=False)
    file = db.Column(db.String(256), nullable=False)

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# 初始化数据库和默认账号
def init_db():
    with app.app_context():
        inspector = inspect(db.engine)
        if not inspector.has_table('users'):
            # 表不存在，创建所有表
            db.create_all()
            # 添加默认管理员账号
            admin = User(username='admin')
            admin.set_password('123456')
            db.session.add(admin)
            db.session.commit()
        else:
            # 表存在，确认是否有 admin 账号之前，确保数据库表已加载
            # 这里直接 try except 捕获异常防止查询出错
            try:
                if not User.query.filter_by(username='admin').first():
                    admin = User(username='admin')
                    admin.set_password('123456')
                    db.session.add(admin)
                    db.session.commit()
            except Exception as e:
                print("查询 users 表时出错，可能表结构不完整，尝试重建表")
                db.create_all()
                admin = User(username='admin')
                admin.set_password('123456')
                db.session.add(admin)
                db.session.commit()
# Flask-Login 用户加载
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 工具函数
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# 登录
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))
    if request.method == 'POST':
        u = User.query.filter_by(username=request.form['username']).first()
        if u and u.check_password(request.form['password']):
            login_user(u)
            flash('登录成功', 'success')
            return redirect(request.args.get('next') or url_for('admin_list'))
        flash('用户名或密码错误', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('已登出', 'info')
    return redirect(url_for('login'))

# OTA 接口（无需登录）
@app.route('/update', methods=['GET', 'POST'])
def update():
    device = request.values.get('device')
    version = request.values.get('firmware')
    dev = Device.query.filter_by(name=device).first()
    if not dev:
        return jsonify({'update': False, 'reason': 'unknown device'})
    if version != dev.version:
        return jsonify({'update': True, 'latest_version': dev.version, 'url': DOWNLOAD_BASE_URL + dev.file})
    return jsonify({'update': False, 'message': 'Already latest'})

# 后台管理列表
@app.route('/admin')
@login_required
def admin_list():
    devices = Device.query.all()
    return render_template('admin_list.html', devices=devices)

# 后台管理表单页面
@app.route('/admin/form', methods=['GET'])
@login_required
def admin_form():
    name = request.args.get('device')
    dev = Device.query.filter_by(name=name).first() if name else None
    return render_template('admin_form.html', device=dev)

# 保存设备信息（新增/修改）
@app.route('/admin/save', methods=['POST'])
@login_required
def admin_save():
    orig = request.form.get('orig_device')
    name = request.form['device'].strip()
    ver = request.form['version'].strip()
    f = request.files.get('firmware')

    if f and f.filename:
        if not allowed_file(f.filename):
            abort(400, "不支持的文件类型")
        ext = f.filename.rsplit('.', 1)[1]
        filename = f"{name}_{uuid.uuid4().hex[:8]}.{ext}"
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        f.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
    else:
        old = Device.query.filter_by(name=orig).first()
        filename = old.file if old else abort(400, "找不到旧固件")

    if orig and orig != name:
        old = Device.query.filter_by(name=orig).first()
        if old:
            db.session.delete(old)

    dev = Device.query.filter_by(name=name).first()
    if not dev:
        dev = Device(name=name, version=ver, file=filename)
        db.session.add(dev)
    else:
        dev.version = ver
        dev.file = filename

    db.session.commit()
    return redirect(url_for('admin_list'))

# 删除设备
@app.route('/admin/delete/<device>', methods=['POST'])
@login_required
def admin_delete(device):
    d = Device.query.filter_by(name=device).first()
    if d:
        db.session.delete(d)
        db.session.commit()
    return redirect(url_for('admin_list'))

# 模拟查询页面
@app.route('/simulate', methods=['GET'])
@login_required
def simulate():
    res = None
    if request.args.get('device'):
        res = update().get_json()
    return render_template('simulate.html', result=res)

# 401 错误页面
@app.errorhandler(401)
def unauthorized(e):
    return render_template('401.html'), 401

# 启动入口
init_db()  # 手动初始化数据库
app.run(host='0.0.0.0', port=8000, debug=True)

