{% extends "base.html" %}
{% block content %}
<h2>设备列表</h2>
<a href="{{ url_for('admin_form') }}" class="btn btn-primary mb-3">添加设备</a>
<table class="table table-striped">
  <thead>
    <tr><th>设备名</th><th>版本</th><th>固件</th><th>操作</th></tr>
  </thead>
  <tbody>
    {% for d in devices %}
    <tr>
      <td>{{ d.name }}</td>
      <td>{{ d.version }}</td>
      <td>{{ d.file }}</td>
      <td>
        <a href="{{ url_for('admin_form', device=d.name) }}" class="btn btn-sm btn-secondary">编辑</a>
        <form action="{{ url_for('admin_delete', device=d.name) }}" method="post" style="display:inline">
          <button class="btn btn-sm btn-danger" onclick="return confirm('确认删除?')">删除</button>
        </form>
      </td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endblock %}

