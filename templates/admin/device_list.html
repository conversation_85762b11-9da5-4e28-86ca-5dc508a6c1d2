{% extends "base.html" %}

{% block content %}
<div class="row mt-3">
    <div class="col-12">
        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-list-ul"></i> 设备管理</h2>
            <div>
                <a href="{{ url_for('admin_device_new') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> 添加设备
                </a>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">总设备数</h6>
                                <h3 class="mb-0">{{ devices.total }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-device-hdd text-primary fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">存储使用</h6>
                                <h3 class="mb-0">{{ storage_stats.total_size_formatted }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-hdd text-info fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">文件数量</h6>
                                <h3 class="mb-0">{{ storage_stats.file_count }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-file-earmark text-success fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">设备分组</h6>
                                <h3 class="mb-0">{{ groups|length }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-collection text-warning fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和过滤 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">搜索设备</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="输入设备名称..." value="{{ search }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">设备分组</label>
                        <select name="group_id" class="form-select">
                            <option value="">所有分组</option>
                            {% for group in groups %}
                                <option value="{{ group.id }}" 
                                        {{ 'selected' if group.id == current_group_id }}>
                                    {{ group.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('admin_list') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">设备列表</h5>
            </div>
            <div class="card-body p-0">
                {% if devices.items %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>设备名称</th>
                                <th>分组</th>
                                <th>当前版本</th>
                                <th>目标版本</th>
                                <th>固件文件</th>
                                <th>文件大小</th>
                                <th>状态</th>
                                <th>最后检查</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for device in devices.items %}
                            <tr>
                                <td>
                                    <strong>{{ device.name }}</strong>
                                    {% if device.description %}
                                        <br><small class="text-muted">{{ device.description }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if device.group %}
                                        <span class="badge bg-secondary">{{ device.group.name }}</span>
                                    {% else %}
                                        <span class="text-muted">无分组</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info version-badge">{{ device.current_version }}</span>
                                </td>
                                <td>
                                    {% if device.target_version %}
                                        <span class="badge bg-success version-badge">{{ device.target_version }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <code>{{ device.firmware_file }}</code>
                                </td>
                                <td>
                                    {% if device.file_size %}
                                        {{ (device.file_size / 1024 / 1024) | round(2) }} MB
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if device.is_active %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> 活跃
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle"></i> 禁用
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if device.last_check_time %}
                                        <small>{{ device.last_check_time.strftime('%m-%d %H:%M') }}</small>
                                    {% else %}
                                        <span class="text-muted">从未</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('admin_device_edit', device_id=device.id) }}" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="confirmDelete('{{ device.name }}', {{ device.id }})" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <h5 class="text-muted mt-3">暂无设备</h5>
                    <p class="text-muted">点击上方按钮添加第一个设备</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 分页 -->
        {% if devices.pages > 1 %}
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                {% if devices.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_list', page=devices.prev_num, search=search, group_id=current_group_id) }}">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in devices.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != devices.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_list', page=page_num, search=search, group_id=current_group_id) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if devices.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_list', page=devices.next_num, search=search, group_id=current_group_id) }}">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除设备 <strong id="deviceName"></strong> 吗？</p>
                <p class="text-danger"><small>此操作将同时删除关联的固件文件，且无法恢复。</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(deviceName, deviceId) {
    document.getElementById('deviceName').textContent = deviceName;
    document.getElementById('deleteForm').action = '/admin/device/' + deviceId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
