{% extends "base.html" %}

{% block content %}
<div class="row mt-3">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="bi bi-{{ 'pencil' if device else 'plus-circle' }}"></i> 
                {{ title or ('编辑设备' if device else '新增设备') }}
            </h2>
            <a href="{{ url_for('admin_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">设备信息</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data" novalidate>
                            {{ form.hidden_tag() }}
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.name.label(class="form-label") }}
                                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                        {% if form.name.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">设备的唯一标识符，只能包含字母、数字、下划线和连字符</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.group_id.label(class="form-label") }}
                                        {{ form.group_id(class="form-select" + (" is-invalid" if form.group_id.errors else "")) }}
                                        {% if form.group_id.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.group_id.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                {{ form.description.label(class="form-label") }}
                                {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.description.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        {{ form.hardware_version.label(class="form-label") }}
                                        {{ form.hardware_version(class="form-control" + (" is-invalid" if form.hardware_version.errors else "")) }}
                                        {% if form.hardware_version.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.hardware_version.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">可选，用于硬件版本匹配</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        {{ form.current_version.label(class="form-label") }}
                                        {{ form.current_version(class="form-control" + (" is-invalid" if form.current_version.errors else "")) }}
                                        {% if form.current_version.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.current_version.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">当前固件版本号</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        {{ form.target_version.label(class="form-label") }}
                                        {{ form.target_version(class="form-control" + (" is-invalid" if form.target_version.errors else "")) }}
                                        {% if form.target_version.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.target_version.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">留空则使用当前版本</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 固件文件上传 -->
                            <div class="mb-3">
                                {{ form.firmware_file.label(class="form-label") }}
                                {% if device and device.firmware_file %}
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        当前固件文件: <code>{{ device.firmware_file }}</code>
                                        {% if device.file_size %}
                                            ({{ (device.file_size / 1024 / 1024) | round(2) }} MB)
                                        {% endif %}
                                        {% if device.file_md5 %}
                                            <br>MD5: <code>{{ device.file_md5 }}</code>
                                        {% endif %}
                                    </div>
                                {% endif %}
                                
                                <div class="file-upload-area">
                                    {{ form.firmware_file(class="form-control" + (" is-invalid" if form.firmware_file.errors else ""), id="firmwareFile") }}
                                    {% if form.firmware_file.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.firmware_file.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="mt-2">
                                        <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                                        <p class="mb-0">点击选择文件或拖拽文件到此处</p>
                                        <small class="text-muted">
                                            支持格式: .bin, .img, .zip, .tar, .gz, .bz2, .xz, .hex, .elf
                                            {% if not device %}(必需){% else %}(可选，不选择则保持当前文件){% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 设备选项 -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        {{ form.auto_update(class="form-check-input") }}
                                        {{ form.auto_update.label(class="form-check-label") }}
                                        <div class="form-text">启用后设备将自动检查更新</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        {{ form.is_active(class="form-check-input") }}
                                        {{ form.is_active.label(class="form-check-label") }}
                                        <div class="form-text">禁用后设备无法获取更新</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-flex justify-content-end">
                                <a href="{{ url_for('admin_list') }}" class="btn btn-secondary me-2">
                                    <i class="bi bi-x-circle"></i> 取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> 
                                    {{ '更新设备' if device else '创建设备' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 侧边栏信息 -->
            <div class="col-lg-4">
                {% if device %}
                <!-- 设备统计 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">设备统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary">{{ device.download_count }}</h4>
                                    <small class="text-muted">下载次数</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info">{{ device.update_histories.count() }}</h4>
                                <small class="text-muted">更新记录</small>
                            </div>
                        </div>
                        <hr>
                        <div class="small">
                            <div class="d-flex justify-content-between">
                                <span>创建时间:</span>
                                <span>{{ device.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>更新时间:</span>
                                <span>{{ device.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                            {% if device.last_check_time %}
                            <div class="d-flex justify-content-between">
                                <span>最后检查:</span>
                                <span>{{ device.last_check_time.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 帮助信息 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-question-circle"></i> 帮助信息</h6>
                    </div>
                    <div class="card-body">
                        <h6>设备名称</h6>
                        <p class="small text-muted">设备的唯一标识符，用于OTA更新时识别设备。建议使用有意义的名称，如产品型号等。</p>
                        
                        <h6>版本管理</h6>
                        <p class="small text-muted">当前版本是设备固件的版本号，目标版本是期望升级到的版本。如果不设置目标版本，则使用当前版本。</p>
                        
                        <h6>固件文件</h6>
                        <p class="small text-muted">支持多种格式的固件文件。上传后系统会自动计算文件的MD5和SHA256校验值。</p>
                        
                        <h6>设备分组</h6>
                        <p class="small text-muted">可以将设备分组管理，便于批量操作和统计。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 文件选择显示
    const fileInput = document.getElementById('firmwareFile');
    const uploadArea = document.querySelector('.file-upload-area');
    
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const fileName = this.files[0].name;
            const fileSize = (this.files[0].size / 1024 / 1024).toFixed(2);
            uploadArea.innerHTML = `
                <i class="bi bi-file-earmark-check fs-1 text-success"></i>
                <p class="mb-0"><strong>${fileName}</strong></p>
                <small class="text-muted">${fileSize} MB</small>
            `;
        }
    });
});
</script>
{% endblock %}
