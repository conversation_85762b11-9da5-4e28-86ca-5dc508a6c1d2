{% extends "base.html" %}

{% block content %}
<div class="row mt-3">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-play-circle"></i> OTA 模拟测试</h2>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <!-- 测试表单 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">模拟设备请求</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" novalidate>
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ form.device.label(class="form-label") }}
                                {{ form.device(class="form-control" + (" is-invalid" if form.device.errors else ""), placeholder="输入设备名称") }}
                                {% if form.device.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.device.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">输入要测试的设备名称</div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.firmware.label(class="form-label") }}
                                {{ form.firmware(class="form-control" + (" is-invalid" if form.firmware.errors else ""), placeholder="输入当前版本号") }}
                                {% if form.firmware.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.firmware.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">输入设备当前的固件版本号</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-play-fill"></i> 执行测试
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- API 说明 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle"></i> API 使用说明</h6>
                    </div>
                    <div class="card-body">
                        <h6>请求地址</h6>
                        <div class="bg-light p-2 rounded mb-3">
                            <code>GET/POST {{ request.url_root }}api/update</code>
                        </div>
                        
                        <h6>请求参数</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>参数名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>device</code></td>
                                    <td>string</td>
                                    <td>设备名称（必需）</td>
                                </tr>
                                <tr>
                                    <td><code>firmware</code></td>
                                    <td>string</td>
                                    <td>当前固件版本（必需）</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <h6>响应示例</h6>
                        <div class="bg-light p-2 rounded">
                            <pre class="mb-0"><code>{
  "update": true,
  "latest_version": "v2.1.0",
  "url": "http://server/static/ota/firmware.bin",
  "file_size": 1048576,
  "md5": "d41d8cd98f00b204e9800998ecf8427e"
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <!-- 测试结果 -->
                {% if result %}
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">测试结果</h5>
                        {% if result.update %}
                            <span class="badge bg-success">需要更新</span>
                        {% else %}
                            <span class="badge bg-info">无需更新</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="bg-light p-3 rounded">
                            <pre class="mb-0"><code>{{ result | tojson(indent=2) }}</code></pre>
                        </div>
                        
                        {% if result.update %}
                        <div class="mt-3">
                            <h6>更新信息</h6>
                            <ul class="list-unstyled">
                                <li><strong>最新版本:</strong> {{ result.latest_version }}</li>
                                {% if result.file_size %}
                                <li><strong>文件大小:</strong> {{ (result.file_size / 1024 / 1024) | round(2) }} MB</li>
                                {% endif %}
                                {% if result.md5 %}
                                <li><strong>MD5校验:</strong> <code>{{ result.md5 }}</code></li>
                                {% endif %}
                                {% if result.url %}
                                <li><strong>下载地址:</strong> <a href="{{ result.url }}" target="_blank">{{ result.url }}</a></li>
                                {% endif %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 快速测试 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-lightning"></i> 快速测试</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">选择已有设备进行快速测试</p>
                        <div id="quickTestButtons">
                            <!-- 这里通过JavaScript动态加载设备列表 -->
                        </div>
                    </div>
                </div>

                <!-- 测试历史 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-clock-history"></i> 最近测试</h6>
                    </div>
                    <div class="card-body">
                        <div class="small text-muted">
                            <p>这里可以显示最近的测试记录...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载设备列表用于快速测试
    loadDevicesForQuickTest();
});

function loadDevicesForQuickTest() {
    // 这里可以通过AJAX加载设备列表
    // 暂时使用静态示例
    const quickTestContainer = document.getElementById('quickTestButtons');
    quickTestContainer.innerHTML = `
        <div class="d-grid gap-2">
            <button class="btn btn-outline-primary btn-sm" onclick="quickTest('device1', 'v1.0.0')">
                device1 (v1.0.0)
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="quickTest('device2', 'v2.0.0')">
                device2 (v2.0.0)
            </button>
        </div>
    `;
}

function quickTest(deviceName, version) {
    document.querySelector('input[name="device"]').value = deviceName;
    document.querySelector('input[name="firmware"]').value = version;
    document.querySelector('form').submit();
}

// 复制API响应
function copyResponse() {
    const responseText = document.querySelector('pre code').textContent;
    navigator.clipboard.writeText(responseText).then(function() {
        // 显示复制成功提示
        const toast = new bootstrap.Toast(document.getElementById('copyToast'));
        toast.show();
    });
}
</script>
{% endblock %}
