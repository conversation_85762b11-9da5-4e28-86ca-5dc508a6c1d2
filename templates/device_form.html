{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-{{ 'pencil' if device else 'plus-circle' }}"></i> 
        {{ title or ('编辑设备' if device else '新增设备') }}
    </h2>
    <a href="{{ url_for('admin_list') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> 返回列表
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">设备信息</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">设备名称 *</label>
                                <input type="text" name="name" class="form-control" required 
                                       value="{{ device.name if device else '' }}"
                                       placeholder="输入设备名称">
                                <div class="form-text">设备的唯一标识符</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">版本号 *</label>
                                <input type="text" name="version" class="form-control" required 
                                       value="{{ device.current_version if device else '' }}"
                                       placeholder="输入版本号">
                                <div class="form-text">固件版本号</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">设备描述</label>
                        <textarea name="description" class="form-control" rows="3" 
                                  placeholder="输入设备描述（可选）">{{ device.description if device else '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">固件文件 {{ '*' if not device else '' }}</label>
                        {% if device and device.firmware_file %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                当前固件文件: <code>{{ device.firmware_file }}</code>
                                {% if device.file_size %}
                                    ({{ "%.1f"|format(device.file_size / 1024 / 1024) }} MB)
                                {% endif %}
                                {% if device.file_md5 %}
                                    <br>MD5: <code>{{ device.file_md5 }}</code>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        <input type="file" name="firmware" class="form-control" id="firmwareFile"
                               {{ 'required' if not device else '' }}
                               accept=".bin,.img,.zip,.tar,.gz,.bz2,.xz,.hex,.elf,.apk">
                        <div class="form-text">
                            支持格式: .bin, .img, .zip, .tar, .gz, .bz2, .xz, .hex, .elf, .apk
                            {% if not device %}(必需){% else %}(可选，不选择则保持当前文件){% endif %}
                        </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>上传进度</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <button type="button" id="cancelUpload" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-x-circle"></i> 取消上传
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" class="form-check-input" 
                                   {{ 'checked' if not device or device.is_active else '' }}>
                            <label class="form-check-label">启用设备</label>
                            <div class="form-text">禁用后设备无法获取更新</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('admin_list') }}" class="btn btn-secondary me-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" id="submitBtn" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            {{ '更新设备' if device else '创建设备' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        {% if device %}
        <!-- 设备统计 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">设备统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ device.download_count }}</h4>
                        <small class="text-muted">下载次数</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ device.download_logs|length }}</h4>
                        <small class="text-muted">下载记录</small>
                    </div>
                </div>
                <hr>
                <div class="small">
                    <div class="d-flex justify-content-between">
                        <span>创建时间:</span>
                        <span>{{ device.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>更新时间:</span>
                        <span>{{ device.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% if device.last_check_time %}
                    <div class="d-flex justify-content-between">
                        <span>最后检查:</span>
                        <span>{{ device.last_check_time.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 下载记录 -->
        {% if device.download_logs %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">最近下载记录</h6>
            </div>
            <div class="card-body">
                {% for log in device.download_logs[-5:] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <small class="text-muted">{{ log.client_ip }}</small>
                    </div>
                    <div>
                        <small>{{ log.download_time.strftime('%m-%d %H:%M') }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endif %}

        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-question-circle"></i> 帮助信息</h6>
            </div>
            <div class="card-body">
                <h6>设备名称</h6>
                <p class="small text-muted">设备的唯一标识符，用于OTA更新时识别设备。</p>
                
                <h6>版本号</h6>
                <p class="small text-muted">当前固件的版本号，客户端会与此版本比较决定是否更新。</p>
                
                <h6>固件文件</h6>
                <p class="small text-muted">支持多种格式的固件文件。上传后系统会自动计算MD5校验值。</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const fileInput = document.getElementById('firmwareFile');
    const submitBtn = document.getElementById('submitBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const cancelBtn = document.getElementById('cancelUpload');

    let uploadXHR = null;

    // 文件选择显示
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const fileName = this.files[0].name;
            const fileSize = (this.files[0].size / 1024 / 1024).toFixed(2);
            console.log(`Selected file: ${fileName} (${fileSize} MB)`);
        }
    });

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        const fileInput = document.getElementById('firmwareFile');

        // 如果没有选择文件，使用默认提交
        if (!fileInput.files || !fileInput.files[0]) {
            return true;
        }

        // 如果有文件，使用AJAX上传
        e.preventDefault();
        uploadWithProgress();
    });

    // 取消上传
    cancelBtn.addEventListener('click', function() {
        if (uploadXHR) {
            uploadXHR.abort();
            resetUploadUI();
        }
    });

    function uploadWithProgress() {
        const formData = new FormData(form);

        // 显示进度条
        uploadProgress.style.display = 'block';
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';

        uploadXHR = new XMLHttpRequest();

        // 上传进度
        uploadXHR.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressText.textContent = Math.round(percentComplete) + '%';
            }
        });

        // 上传完成
        uploadXHR.addEventListener('load', function() {
            if (uploadXHR.status === 200) {
                // 成功，重定向到列表页
                window.location.href = '{{ url_for("admin_list") }}';
            } else {
                // 失败，显示错误
                alert('上传失败: ' + uploadXHR.statusText);
                resetUploadUI();
            }
        });

        // 上传错误
        uploadXHR.addEventListener('error', function() {
            alert('上传失败: 网络错误');
            resetUploadUI();
        });

        // 上传被取消
        uploadXHR.addEventListener('abort', function() {
            alert('上传已取消');
            resetUploadUI();
        });

        // 发送请求
        uploadXHR.open('POST', form.action);
        uploadXHR.send(formData);
    }

    function resetUploadUI() {
        uploadProgress.style.display = 'none';
        progressBar.style.width = '0%';
        progressText.textContent = '0%';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> {{ "更新设备" if device else "创建设备" }}';
        uploadXHR = null;
    }
});
</script>
{% endblock %}
