<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title or "OTA 管理系统" }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
            padding: 2rem;
            text-align: center;
            transition: border-color 0.15s ease-in-out;
        }
        .file-upload-area:hover {
            border-color: #0d6efd;
        }
        .file-upload-area.dragover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        .stats-card {
            border-left: 4px solid #0d6efd;
        }
        .device-status-active {
            color: #198754;
        }
        .device-status-inactive {
            color: #dc3545;
        }
        .version-badge {
            font-family: 'Courier New', monospace;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-cloud-download"></i> OTA 管理系统
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                {% if current_user.is_authenticated %}
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_list') }}">
                            <i class="bi bi-list-ul"></i> 设备管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_groups') }}">
                            <i class="bi bi-collection"></i> 分组管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('simulate') }}">
                            <i class="bi bi-play-circle"></i> 模拟测试
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="bi bi-box-arrow-right"></i> 登出</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid">
        <!-- Flash 消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="row mt-3">
                    <div class="col-12">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {% if category == 'success' %}
                                    <i class="bi bi-check-circle-fill"></i>
                                {% elif category == 'danger' or category == 'error' %}
                                    <i class="bi bi-exclamation-triangle-fill"></i>
                                {% elif category == 'warning' %}
                                    <i class="bi bi-exclamation-circle-fill"></i>
                                {% else %}
                                    <i class="bi bi-info-circle-fill"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 自定义 JavaScript -->
    <script>
        // 自动隐藏 alert
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // 文件拖拽上传
        document.addEventListener('DOMContentLoaded', function() {
            var uploadAreas = document.querySelectorAll('.file-upload-area');
            uploadAreas.forEach(function(area) {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    area.classList.add('dragover');
                });

                area.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    area.classList.remove('dragover');
                });

                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    area.classList.remove('dragover');

                    var files = e.dataTransfer.files;
                    if (files.length > 0) {
                        var fileInput = area.querySelector('input[type="file"]');
                        if (fileInput) {
                            fileInput.files = files;
                            // 触发 change 事件
                            var event = new Event('change', { bubbles: true });
                            fileInput.dispatchEvent(event);
                        }
                    }
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

