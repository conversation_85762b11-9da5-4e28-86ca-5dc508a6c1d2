{% extends "base.html" %}
{% block content %}
<h2>{{ device and '编辑设备' or '添加设备' }}</h2>
<form action="{{ url_for('admin_save') }}" method="post" enctype="multipart/form-data">
  <input type="hidden" name="orig_device" value="{{ device.name if device else '' }}">
  <div class="mb-3">
    <label class="form-label">设备名</label>
    <input type="text" name="device" class="form-control" required value="{{ device.name if device else '' }}">
  </div>
  <div class="mb-3">
    <label class="form-label">版本号</label>
    <input type="text" name="version" class="form-control" required value="{{ device.version if device else '' }}">
  </div>
  <div class="mb-3">
    <label class="form-label">固件文件</label>
    {% if device %}
      <p>当前固件: <code>{{ device.file }}</code></p>
    {% endif %}
    <input type="file" name="firmware" class="form-control" {{ device and '' or 'required' }}>
  </div>
  <button class="btn btn-success">保存</button>
  <a href="{{ url_for('admin_list') }}" class="btn btn-secondary">取消</a>
</form>
{% endblock %}

